// src/services/databaseService.js
// Database service to replace KV operations with D1 SQL queries

export class DatabaseService {
  constructor(env) {
    this.db = env.DB;
  }

  // User operations
  async createUser(userData) {
    const { id, email, password, type = 'api', status = 'active', created_at, updated_at } = userData;
    
    const result = await this.db.prepare(`
      INSERT INTO users (id, email, password, type, status, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `).bind(id, email, password, type, status, created_at, updated_at).run();
    
    return result.success;
  }

  async getUserById(userId) {
    const result = await this.db.prepare(`
      SELECT * FROM users WHERE id = ?
    `).bind(userId).first();
    
    return result;
  }

  async getUserByEmail(email) {
    const result = await this.db.prepare(`
      SELECT * FROM users WHERE email = ?
    `).bind(email).first();
    
    return result;
  }

  async updateUser(userId, userData) {
    const { email, password, type, status, updated_at } = userData;
    
    const result = await this.db.prepare(`
      UPDATE users 
      SET email = ?, password = ?, type = ?, status = ?, updated_at = ?
      WHERE id = ?
    `).bind(email, password, type, status, updated_at, userId).run();
    
    return result.success;
  }

  async deleteUser(userId) {
    const result = await this.db.prepare(`
      DELETE FROM users WHERE id = ?
    `).bind(userId).run();
    
    return result.success;
  }

  // Domain operations
  async createDomain(domainData) {
    const { id, user_id, domain, api_key, status = 'active', tier = 'free', created_at, activated_at } = domainData;
    
    const result = await this.db.prepare(`
      INSERT INTO domains (id, user_id, domain, api_key, status, tier, created_at, activated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `).bind(id, user_id, domain, api_key, status, tier, created_at, activated_at).run();
    
    return result.success;
  }

  async getDomainByName(domain) {
    const result = await this.db.prepare(`
      SELECT * FROM domains WHERE domain = ?
    `).bind(domain).first();
    
    return result;
  }

  async getDomainsByUserId(userId) {
    const result = await this.db.prepare(`
      SELECT * FROM domains WHERE user_id = ?
    `).bind(userId).all();
    
    return result.results || [];
  }

  async updateDomain(domainId, domainData) {
    const { status, tier, activated_at } = domainData;
    
    const result = await this.db.prepare(`
      UPDATE domains 
      SET status = ?, tier = ?, activated_at = ?
      WHERE id = ?
    `).bind(status, tier, activated_at, domainId).run();
    
    return result.success;
  }

  async deleteDomain(domainId) {
    const result = await this.db.prepare(`
      DELETE FROM domains WHERE id = ?
    `).bind(domainId).run();
    
    return result.success;
  }

  // API Key operations
  async createApiKeyMapping(apiKey, userId, domainId) {
    const result = await this.db.prepare(`
      INSERT INTO api_keys (api_key, user_id, domain_id, created_at)
      VALUES (?, ?, ?, ?)
    `).bind(apiKey, userId, domainId, new Date().toISOString()).run();
    
    return result.success;
  }

  async getUserIdByApiKey(apiKey) {
    const result = await this.db.prepare(`
      SELECT user_id FROM api_keys WHERE api_key = ?
    `).bind(apiKey).first();
    
    return result?.user_id;
  }

  async deleteApiKeyMapping(apiKey) {
    const result = await this.db.prepare(`
      DELETE FROM api_keys WHERE api_key = ?
    `).bind(apiKey).run();
    
    return result.success;
  }

  // Portal credentials operations
  async createPortalCredentials(userId, credentialsData) {
    const { plain_password, hashed_password, created_at } = credentialsData;
    
    const result = await this.db.prepare(`
      INSERT OR REPLACE INTO portal_credentials (user_id, plain_password, hashed_password, created_at)
      VALUES (?, ?, ?, ?)
    `).bind(userId, plain_password, hashed_password, created_at).run();
    
    return result.success;
  }

  async getPortalCredentials(userId) {
    const result = await this.db.prepare(`
      SELECT * FROM portal_credentials WHERE user_id = ?
    `).bind(userId).first();
    
    return result;
  }

  // Subscription operations
  async createSubscription(subscriptionData) {
    const { 
      subscription_id, plan_id, product_id, user_id, tier, base_price, 
      addons, total_price, status, created_at, activated_at, updated_at, next_billing_time 
    } = subscriptionData;
    
    const result = await this.db.prepare(`
      INSERT INTO subscriptions 
      (subscription_id, plan_id, product_id, user_id, tier, base_price, addons, total_price, status, created_at, activated_at, updated_at, next_billing_time)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).bind(
      subscription_id, plan_id, product_id, user_id, tier, base_price, 
      JSON.stringify(addons), total_price, status, created_at, activated_at, updated_at, next_billing_time
    ).run();
    
    return result.success;
  }

  async getSubscription(subscriptionId) {
    const result = await this.db.prepare(`
      SELECT * FROM subscriptions WHERE subscription_id = ?
    `).bind(subscriptionId).first();
    
    if (result && result.addons) {
      result.addons = JSON.parse(result.addons);
    }
    
    return result;
  }

  async updateSubscription(subscriptionId, subscriptionData) {
    const { status, updated_at, next_billing_time } = subscriptionData;
    
    const result = await this.db.prepare(`
      UPDATE subscriptions 
      SET status = ?, updated_at = ?, next_billing_time = ?
      WHERE subscription_id = ?
    `).bind(status, updated_at, next_billing_time, subscriptionId).run();
    
    return result.success;
  }

  // Payment operations
  async createPayment(paymentData) {
    const { id, user_id, subscription_id, amount_currency, amount_value, time, recorded_at, status = 'completed' } = paymentData;
    
    const result = await this.db.prepare(`
      INSERT INTO payments (id, user_id, subscription_id, amount_currency, amount_value, time, recorded_at, status)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `).bind(id, user_id, subscription_id, amount_currency, amount_value, time, recorded_at, status).run();
    
    return result.success;
  }

  async getPayment(paymentId) {
    const result = await this.db.prepare(`
      SELECT * FROM payments WHERE id = ?
    `).bind(paymentId).first();
    
    return result;
  }

  // Webhook event operations
  async createWebhookEvent(eventData) {
    const { 
      id, event_type, event_version, create_time, resource_type, 
      resource_version, summary, resource_data, received_at, processed = false 
    } = eventData;
    
    const result = await this.db.prepare(`
      INSERT INTO webhook_events 
      (id, event_type, event_version, create_time, resource_type, resource_version, summary, resource_data, received_at, processed)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).bind(
      id, event_type, event_version, create_time, resource_type, 
      resource_version, summary, JSON.stringify(resource_data), received_at, processed
    ).run();
    
    return result.success;
  }

  async getWebhookEvent(eventId) {
    const result = await this.db.prepare(`
      SELECT * FROM webhook_events WHERE id = ?
    `).bind(eventId).first();
    
    if (result && result.resource_data) {
      result.resource_data = JSON.parse(result.resource_data);
    }
    
    return result;
  }

  // Tier settings operations
  async createOrUpdateTierSettings(config, version) {
    const updated_at = new Date().toISOString();
    
    const result = await this.db.prepare(`
      INSERT OR REPLACE INTO tier_settings (id, config, updated_at, version)
      VALUES (1, ?, ?, ?)
    `).bind(JSON.stringify(config), updated_at, version).run();
    
    return result.success;
  }

  async getTierSettings() {
    const result = await this.db.prepare(`
      SELECT * FROM tier_settings WHERE id = 1
    `).first();
    
    if (result && result.config) {
      result.config = JSON.parse(result.config);
    }
    
    return result;
  }

  // User tier operations
  async setUserTier(userId, tier) {
    const updated_at = new Date().toISOString();
    
    const result = await this.db.prepare(`
      INSERT OR REPLACE INTO user_tiers (user_id, tier, updated_at)
      VALUES (?, ?, ?)
    `).bind(userId, tier, updated_at).run();
    
    return result.success;
  }

  async getUserTier(userId) {
    const result = await this.db.prepare(`
      SELECT * FROM user_tiers WHERE user_id = ?
    `).bind(userId).first();
    
    return result;
  }

  // Email tier operations
  async setEmailTier(email, tier) {
    const updated_at = new Date().toISOString();
    
    const result = await this.db.prepare(`
      INSERT OR REPLACE INTO email_tiers (email, tier, updated_at)
      VALUES (?, ?, ?)
    `).bind(email, tier, updated_at).run();
    
    return result.success;
  }

  async getEmailTier(email) {
    const result = await this.db.prepare(`
      SELECT * FROM email_tiers WHERE email = ?
    `).bind(email).first();
    
    return result;
  }

  // Quota usage operations
  async getQuotaUsage(userId, type) {
    const result = await this.db.prepare(`
      SELECT * FROM quota_usage WHERE user_id = ? AND type = ?
    `).bind(userId, type).first();
    
    return result;
  }

  async updateQuotaUsage(userId, type, usageData) {
    const { count, last_reset, reset_date } = usageData;
    
    const result = await this.db.prepare(`
      INSERT OR REPLACE INTO quota_usage (user_id, type, count, last_reset, reset_date)
      VALUES (?, ?, ?, ?, ?)
    `).bind(userId, type, count, last_reset, reset_date).run();
    
    return result.success;
  }

  // Email queue operations
  async createEmailQueueItem(queueData) {
    const { id, user_id, type, status = 'pending', data, attempts = 0, queued_at, process_after } = queueData;
    
    const result = await this.db.prepare(`
      INSERT INTO email_queue (id, user_id, type, status, data, attempts, queued_at, process_after)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `).bind(id, user_id, type, status, JSON.stringify(data), attempts, queued_at, process_after).run();
    
    return result.success;
  }

  async getEmailQueueItem(id) {
    const result = await this.db.prepare(`
      SELECT * FROM email_queue WHERE id = ?
    `).bind(id).first();
    
    if (result && result.data) {
      result.data = JSON.parse(result.data);
    }
    
    return result;
  }

  async getPendingEmailQueueItems() {
    const result = await this.db.prepare(`
      SELECT * FROM email_queue 
      WHERE status = 'pending' AND process_after <= ?
      ORDER BY queued_at ASC
    `).bind(new Date().toISOString()).all();
    
    return (result.results || []).map(item => {
      if (item.data) {
        item.data = JSON.parse(item.data);
      }
      return item;
    });
  }

  async updateEmailQueueItem(id, updateData) {
    const { status, attempts, sent_at, error_message } = updateData;
    
    const result = await this.db.prepare(`
      UPDATE email_queue 
      SET status = ?, attempts = ?, sent_at = ?, error_message = ?
      WHERE id = ?
    `).bind(status, attempts, sent_at, error_message, id).run();
    
    return result.success;
  }

  async deleteEmailQueueItem(id) {
    const result = await this.db.prepare(`
      DELETE FROM email_queue WHERE id = ?
    `).bind(id).run();
    
    return result.success;
  }

  async getEmailQueueStats() {
    const result = await this.db.prepare(`
      SELECT 
        status,
        COUNT(*) as count
      FROM email_queue 
      GROUP BY status
    `).all();
    
    const stats = { pending: 0, sent: 0, failed: 0 };
    (result.results || []).forEach(row => {
      stats[row.status] = row.count;
    });
    
    return stats;
  }

  // Usage history operations
  async createUsageHistory(usageData) {
    const { user_id, api_key, domain, endpoint, type, timestamp, success = true } = usageData;
    
    const result = await this.db.prepare(`
      INSERT INTO usage_history (user_id, api_key, domain, endpoint, type, timestamp, success)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `).bind(user_id, api_key, domain, endpoint, type, timestamp, success).run();
    
    return result.success;
  }

  async getUserUsageHistory(userId, limit = 100) {
    const result = await this.db.prepare(`
      SELECT * FROM usage_history 
      WHERE user_id = ?
      ORDER BY timestamp DESC
      LIMIT ?
    `).bind(userId, limit).all();
    
    return result.results || [];
  }

  // Utility methods for complex operations
  async getUserWithDomains(userId) {
    const user = await this.getUserById(userId);
    if (!user) return null;
    
    const domains = await this.getDomainsByUserId(userId);
    user.domains = domains;
    
    return user;
  }

  async getUserWithDomainsAndCredentials(userId) {
    const user = await this.getUserWithDomains(userId);
    if (!user) return null;
    
    if (user.type === 'portal') {
      const credentials = await this.getPortalCredentials(userId);
      user.credentials = credentials;
    }
    
    return user;
  }

  // Portal user specific operations
  async createPortalUser(userData) {
    const {
      id, email, password, type = 'portal', status = 'active',
      created_at, updated_at, last_login = null, is_active = 1,
      activated_at = null, google_id = null, picture = null, profile_data = null
    } = userData;

    const result = await this.db.prepare(`
      INSERT INTO users (
        id, email, password, type, status, created_at, updated_at,
        last_login, is_active, activated_at, google_id, picture, profile_data
      )
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).bind(
      id, email, password, type, status, created_at, updated_at,
      last_login, is_active, activated_at, google_id, picture, profile_data
    ).run();

    return result.success;
  }

  async updatePortalUser(userId, updateData) {
    const {
      last_login, is_active, activated_at, google_id, picture, profile_data, updated_at
    } = updateData;

    const result = await this.db.prepare(`
      UPDATE users
      SET last_login = ?, is_active = ?, activated_at = ?, google_id = ?,
          picture = ?, profile_data = ?, updated_at = ?
      WHERE id = ? AND type = 'portal'
    `).bind(
      last_login, is_active, activated_at, google_id, picture, profile_data, updated_at, userId
    ).run();

    return result.success;
  }

  async getPortalUserByEmail(email) {
    const result = await this.db.prepare(`
      SELECT * FROM users WHERE email = ? AND type = 'portal'
    `).bind(email).first();

    return result;
  }

  async getPortalUserById(userId) {
    const result = await this.db.prepare(`
      SELECT * FROM users WHERE id = ? AND type = 'portal'
    `).bind(userId).first();

    return result;
  }

  // Activation token operations
  async createActivationToken(tokenData) {
    const { token, user_id, created_at, expires_at = null } = tokenData;

    const result = await this.db.prepare(`
      INSERT INTO activation_tokens (token, user_id, created_at, expires_at)
      VALUES (?, ?, ?, ?)
    `).bind(token, user_id, created_at, expires_at).run();

    return result.success;
  }

  async getActivationToken(token) {
    const result = await this.db.prepare(`
      SELECT * FROM activation_tokens WHERE token = ? AND used = FALSE
    `).bind(token).first();

    return result;
  }

  async markActivationTokenUsed(token) {
    const result = await this.db.prepare(`
      UPDATE activation_tokens SET used = TRUE WHERE token = ?
    `).bind(token).run();

    return result.success;
  }

  async deleteActivationToken(token) {
    const result = await this.db.prepare(`
      DELETE FROM activation_tokens WHERE token = ?
    `).bind(token).run();

    return result.success;
  }

  // Password reset token operations
  async createPasswordResetToken(tokenData) {
    const { token, user_id, created_at, expires_at } = tokenData;

    const result = await this.db.prepare(`
      INSERT INTO password_reset_tokens (token, user_id, created_at, expires_at)
      VALUES (?, ?, ?, ?)
    `).bind(token, user_id, created_at, expires_at).run();

    return result.success;
  }

  async getPasswordResetToken(token) {
    const result = await this.db.prepare(`
      SELECT * FROM password_reset_tokens WHERE token = ? AND used = FALSE
    `).bind(token).first();

    return result;
  }

  async markPasswordResetTokenUsed(token) {
    const result = await this.db.prepare(`
      UPDATE password_reset_tokens SET used = TRUE, used_at = ? WHERE token = ?
    `).bind(new Date().toISOString(), token).run();

    return result.success;
  }

  async deletePasswordResetToken(token) {
    const result = await this.db.prepare(`
      DELETE FROM password_reset_tokens WHERE token = ?
    `).bind(token).run();

    return result.success;
  }

  // Get all portal users
  async getAllPortalUsers() {
    const result = await this.db.prepare(`
      SELECT * FROM users WHERE type = 'portal' ORDER BY created_at DESC
    `).all();

    return result.results || [];
  }

  // Delete portal user and all related data
  async deletePortalUser(userId) {
    // Delete in order to respect foreign key constraints
    const operations = [
      () => this.db.prepare('DELETE FROM activation_tokens WHERE user_id = ?').bind(userId).run(),
      () => this.db.prepare('DELETE FROM password_reset_tokens WHERE user_id = ?').bind(userId).run(),
      () => this.db.prepare('DELETE FROM portal_credentials WHERE user_id = ?').bind(userId).run(),
      () => this.db.prepare('DELETE FROM email_queue WHERE user_id = ?').bind(userId).run(),
      () => this.db.prepare('DELETE FROM quota_usage WHERE user_id = ?').bind(userId).run(),
      () => this.db.prepare('DELETE FROM usage_history WHERE user_id = ?').bind(userId).run(),
      () => this.db.prepare('DELETE FROM api_keys WHERE user_id = ?').bind(userId).run(),
      () => this.db.prepare('DELETE FROM domains WHERE user_id = ?').bind(userId).run(),
      () => this.db.prepare('DELETE FROM subscriptions WHERE user_id = ?').bind(userId).run(),
      () => this.db.prepare('DELETE FROM payments WHERE user_id = ?').bind(userId).run(),
      () => this.db.prepare('DELETE FROM users WHERE id = ?').bind(userId).run()
    ];

    const results = [];
    for (const operation of operations) {
      try {
        const result = await operation();
        results.push(result);
      } catch (error) {
        console.error('Delete operation failed:', error);
        // Continue with other operations
      }
    }

    return results;
  }

  // Methods to get data by email for dashboard
  async getSubscriptionsByEmail(email) {
    // First get user by email
    const user = await this.getUserByEmail(email);
    if (!user) return [];

    const result = await this.db.prepare(`
      SELECT * FROM subscriptions WHERE user_id = ?
    `).bind(user.id).all();

    return (result.results || []).map(sub => {
      if (sub.addons) {
        sub.addons = JSON.parse(sub.addons);
      }
      return sub;
    });
  }

  async getPaymentsByEmail(email) {
    // First get user by email
    const user = await this.getUserByEmail(email);
    if (!user) return [];

    const result = await this.db.prepare(`
      SELECT * FROM payments WHERE user_id = ?
    `).bind(user.id).all();

    return result.results || [];
  }

  async getWebhookEventsByEmail(email) {
    // For webhook events, we need to search in the resource_data JSON
    // This is a simplified approach - in practice you might want to index this differently
    const result = await this.db.prepare(`
      SELECT * FROM webhook_events
      WHERE resource_data LIKE ?
    `).bind(`%${email}%`).all();

    return (result.results || []).map(event => {
      if (event.resource_data) {
        event.resource_data = JSON.parse(event.resource_data);
      }
      return event;
    });
  }

  async getEmailQueueByEmail(email) {
    // First get user by email
    const user = await this.getUserByEmail(email);
    const emailQueueItems = [];

    // Get by user_id if user exists
    if (user) {
      const result = await this.db.prepare(`
        SELECT * FROM email_queue WHERE user_id = ?
      `).bind(user.id).all();

      emailQueueItems.push(...(result.results || []));
    }

    // Also search by email in the data field
    const emailResult = await this.db.prepare(`
      SELECT * FROM email_queue
      WHERE data LIKE ? OR data LIKE ?
    `).bind(`%"email":"${email}"%`, `%"to":"${email}"%`).all();

    emailQueueItems.push(...(emailResult.results || []));

    // Remove duplicates and parse data
    const uniqueItems = emailQueueItems.filter((item, index, self) =>
      index === self.findIndex(i => i.id === item.id)
    );

    return uniqueItems.map(item => {
      if (item.data) {
        item.data = JSON.parse(item.data);
      }
      return item;
    });
  }

  // Transaction support for complex operations
  async executeTransaction(operations) {
    // D1 doesn't support explicit transactions yet, but we can batch operations
    const results = [];
    for (const operation of operations) {
      try {
        const result = await operation();
        results.push(result);
      } catch (error) {
        console.error('Transaction operation failed:', error);
        throw error;
      }
    }
    return results;
  }
}